import hashlib
import json
import operator
import os
import pickle
import sqlite3
from enum import Enum
from pathlib import Path
from pprint import pprint
from typing import Annotated, Any, Dict, List, TypedDict
from uuid import uuid4

import pandas as pd
import polars as pl
from langchain.chat_models import init_chat_model
from langchain.output_parsers import RetryOutputParser
from langchain_core.messages import (
    AIMessage,
    AnyMessage,
    BaseMessage,
    HumanMessage,
    SystemMessage,
)
from langchain_core.output_parsers import <PERSON>sonOutputParser, StrOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnableLambda
from langgraph.checkpoint.sqlite import SqliteSaver
from langgraph.graph import END, START, MessagesState, StateGraph, add_messages

# --- Assume these imports match your project structure ---
from langgraph_da_backend.basic_tools.data_cleaning import (
    handle_duplicates,
    handle_missing_values,
    handle_outliers,
    perform_normalization,
    perform_sorting,
)
from langgraph_da_backend.basic_tools.data_describe import describe
from langgraph_da_backend.exec_engine.utils import (
    extract_python_code,
    run_sandboxed_code,
)
from langgraph_da_backend.expert_tools.pseudo_tool_list import *
from langgraph_da_backend.schemas.agent import DataSource
from langgraph_da_backend.utils.loader.csv import load as load_csv
from langgraph_da_backend.utils.static.prompt_schema import (
    AbstractPlanSchema,
    DataProcessingPipelineSchema,
    RequestSanityCheckSchema,
    RewriteSchema,
)
from langgraph_da_backend.utils.static.prompt_template import (
    ABSTRACT_PLAN_PARSE_TEMPLATE,
    ABSTRACT_PLAN_TEMPLATE,
    DA_REWRITE_TEMPLATE,
    DA_SANITY_CHECK_TEMPLATE,
    DE_CODE_CURATION_TEMPLATE,
    DE_PIPELINE_TEMPLATE,
)
from langgraph_da_backend.utils.tool.tool_card import REGISTERED_TOOLS
from loguru import logger
from pydantic import BaseModel, Field

# --- Simple File Cache Utility ---
CACHE_DIR = Path(".file_cache")
CACHE_DIR.mkdir(exist_ok=True)


def save_to_cache(obj: Any) -> str:
    """Saves an object to a file and returns the file path."""
    file_path = str(CACHE_DIR / f"{uuid4()}.pkl")
    with open(file_path, "wb") as f:
        pickle.dump(obj, f)
    return file_path


def load_from_cache(path: str) -> Any:
    """Loads an object from a file path."""
    with open(path, "rb") as f:
        return pickle.load(f)


# --- Modified AgentState ---
class AgentState(TypedDict):
    messages: Annotated[List[AnyMessage], add_messages]
    current_user_prompt: str
    terminate_signal: bool
    target_data_sources: List[str]
    loaded_data_paths: List[str]
    refined_data_path: str | None
    renewed_data_path: str | None
    abstract_plan_desc: str
    abstract_plan_feedback: str
    reflection_hint: str
    tool_exec_results: List[Any]
    execution_reflection_feedback: str
    summary_report: Any


# --- Updated Graph Nodes with Original Logic Restored ---


def standby(state: AgentState) -> AgentState:
    if state.get("target_data_sources"):
        loaded_paths = []
        if state.get("loaded_data_paths"):
            for path in state["loaded_data_paths"]:
                cached_obj = load_from_cache(path)
                loaded_paths.append(cached_obj.uri)

        for data_source_uri in state["target_data_sources"]:
            try:
                uri = Path(data_source_uri).resolve().as_posix()
                if uri in loaded_paths:
                    continue
                d = load_csv(uri)
                protocol = "csv"
                data_source_obj = DataSource(uri=uri, protocol=protocol, data=d)
                cached_path = save_to_cache(data_source_obj)
                if not state.get("loaded_data_paths"):
                    state["loaded_data_paths"] = []
                state["loaded_data_paths"].append(cached_path)
                logger.info(
                    f"Loaded and cached data from {uri} at {cached_path}"
                )
            except Exception as e:
                logger.error(f"Failed to load data from {data_source_uri}: {e}")

    if state.get("current_user_prompt"):
        if not state.get("messages"):
            state["messages"] = []
        state["messages"].append(
            HumanMessage(content=state["current_user_prompt"])
        )
    state["target_data_sources"] = []
    return state


def standby_routing(state: AgentState) -> str:
    if state.get("terminate_signal"):
        return "END"
    elif state.get("loaded_data_paths") and state.get("current_user_prompt"):
        return "abstract_plan"
    else:
        return "standby"


def abstract_plan(state: AgentState) -> AgentState:
    # Load data from cache for planning
    data_source_obj = load_from_cache(state["loaded_data_paths"][0])
    data_for_planning = data_source_obj.data

    # <<< START OF YOUR ORIGINAL LOGIC >>>
    llm = init_chat_model(
        "gpt-4.1",
        model_provider="openai",
        base_url="https://api.apiyi.com/v1",
        streaming=False,
        temperature=0,
        use_responses_api=False,
    )
    sanity_parser = JsonOutputParser(pydantic_object=RequestSanityCheckSchema)
    retry_parser = RetryOutputParser.from_llm(parser=sanity_parser, llm=llm)
    sanity_prompt = PromptTemplate(
        template=DA_SANITY_CHECK_TEMPLATE,
        input_variables=["query"],
        partial_variables={
            "format_instructions": sanity_parser.get_format_instructions()
        },
    )
    sanity_chain = {
        "prompt": sanity_prompt,
        "llm_output": sanity_prompt | llm | StrOutputParser(),
    } | RunnableLambda(
        lambda x: retry_parser.parse_with_prompt(x["llm_output"], x["prompt"])
    )
    sanity_result = sanity_chain.invoke({"query": state["current_user_prompt"]})
    logger.debug(sanity_result)

    if not sanity_result.get("is_da_request"):
        return state

    rewrite_parser = JsonOutputParser(pydantic_object=RewriteSchema)
    retry_parser = RetryOutputParser.from_llm(parser=rewrite_parser, llm=llm)
    rewrite_prompt = PromptTemplate(
        template=DA_REWRITE_TEMPLATE,
        input_variables=["query"],
        partial_variables={
            "format_instructions": rewrite_parser.get_format_instructions(),
            "data_schema": data_for_planning.collect_schema(),
        },
    )
    rewrite_chain = {
        "prompt": rewrite_prompt,
        "llm_output": rewrite_prompt | llm | StrOutputParser(),
    } | RunnableLambda(
        lambda x: retry_parser.parse_with_prompt(x["llm_output"], x["prompt"])
    )
    rewrite_result = rewrite_chain.invoke(
        {"query": state["current_user_prompt"]}
    )
    logger.debug(rewrite_result)
    rewritten_query = rewrite_result.get("rewritten_query")

    plan_prompt = PromptTemplate(
        template=ABSTRACT_PLAN_TEMPLATE,
        input_variables=["query"],
        partial_variables={
            "data_description": describe(
                data_for_planning, do_corr_analysis=False
            )
        },
    )
    plan_chain = plan_prompt | llm
    plan_result_str = plan_chain.invoke({"query": rewritten_query})

    abstract_plan_parser = JsonOutputParser(pydantic_object=AbstractPlanSchema)
    retry_parser = RetryOutputParser.from_llm(
        parser=abstract_plan_parser, llm=llm
    )
    plan_parse_prompt = PromptTemplate(
        template=ABSTRACT_PLAN_PARSE_TEMPLATE,
        input_variables=["query"],
        partial_variables={
            "format_instructions": abstract_plan_parser.get_format_instructions()
        },
    )
    plan_parse_chain = {
        "prompt": plan_parse_prompt,
        "llm_output": plan_parse_prompt | llm | StrOutputParser(),
    } | RunnableLambda(
        lambda x: retry_parser.parse_with_prompt(x["llm_output"], x["prompt"])
    )
    plan_result = plan_parse_chain.invoke({"query": plan_result_str})
    logger.debug(plan_result)

    state["abstract_plan_desc"] = json.dumps(plan_result)
    # <<< END OF YOUR ORIGINAL LOGIC >>>

    return state


def data_engineering(state: AgentState) -> AgentState:
    initial_data_path = state["loaded_data_paths"][0]
    initial_data_source = load_from_cache(initial_data_path)
    data_object_lazy = initial_data_source.data

    # <<< START OF YOUR ORIGINAL LOGIC >>>
    llm_pipeline = init_chat_model(
        "gpt-4.1",
        model_provider="openai",
        base_url="https://api.apiyi.com/v1",
        streaming=False,
        temperature=0,
        use_responses_api=False,
    )
    llm_code = init_chat_model(
        "gemini-2.5-pro",
        model_provider="openai",
        base_url="https://api.apiyi.com/v1",
        streaming=False,
    )

    pipeline_parser = JsonOutputParser(
        pydantic_object=DataProcessingPipelineSchema
    )
    retry_parser = RetryOutputParser.from_llm(
        parser=pipeline_parser, llm=llm_pipeline
    )
    pipeline_prompt = PromptTemplate(
        template=DE_PIPELINE_TEMPLATE,
        input_variables=["curation_hint"],
        partial_variables={
            "format_instructions": pipeline_parser.get_format_instructions(),
            "data_schema": data_object_lazy.collect_schema(),
        },
    )
    pipeline_chain = {
        "prompt": pipeline_prompt,
        "llm_output": pipeline_prompt | llm_pipeline | StrOutputParser(),
    } | RunnableLambda(
        lambda x: retry_parser.parse_with_prompt(x["llm_output"], x["prompt"])
    )
    pipeline_result = pipeline_chain.invoke(
        {
            "curation_hint": json.loads(state["abstract_plan_desc"])[
                "data_preparation_plan"
            ]
        }
    )

    data_object = data_object_lazy.collect().clone()

    # This logic is correct, it sequentially applies transformations
    if pipeline_result.get("handle_missing_values"):
        data_object = handle_missing_values(
            data_object, **pipeline_result["handle_missing_values"]
        )["df"]
    if pipeline_result.get("handle_outliers"):
        data_object = handle_outliers(
            data_object, **pipeline_result["handle_outliers"]
        )["df"]
    if pipeline_result.get("handle_duplicates"):
        data_object = handle_duplicates(
            data_object, **pipeline_result["handle_duplicates"]
        )["df"]
    if pipeline_result.get("perform_sorting"):
        data_object = perform_sorting(
            data_object, **pipeline_result["perform_sorting"]
        )["df"]
    if pipeline_result.get("perform_normalization"):
        data_object = perform_normalization(
            data_object, **pipeline_result["perform_normalization"]
        )["df"]

    # <<< END OF YOUR ORIGINAL LOGIC >>>

    if pipeline_result.get("further_curation"):
        curation_prompt = PromptTemplate(
            template=DE_CODE_CURATION_TEMPLATE,
            input_variables=["curation_hint"],
            partial_variables={"data_schema": data_object.collect_schema()},
        )
        curation_chain = curation_prompt | llm_code | StrOutputParser()
        curation_result = curation_chain.invoke(
            {"curation_hint": pipeline_result["further_curation"]}
        )
        curation_code = extract_python_code(curation_result)
        logger.debug(curation_code)
        outcome, df_refined, df_new = run_sandboxed_code(
            curation_code, data_object.to_pandas()
        )

        state["refined_data_path"] = save_to_cache(df_refined)
        state["renewed_data_path"] = save_to_cache(df_new)
        pprint(outcome)
    else:
        state["refined_data_path"] = save_to_cache(data_object)
        state["renewed_data_path"] = None

    logger.info("Data engineering complete. Data saved to cache.")
    return state


def data_analysis(state: AgentState) -> AgentState:
    logger.info("--- Entering Data Analysis Node ---")
    if state.get("refined_data_path"):
        refined_df = load_from_cache(state["refined_data_path"])
    else:
        logger.warning("Refined data path not found. Skipping data analysis.")
        return state

    if state.get("renewed_data_path"):
        renewed_df = load_from_cache(state["renewed_data_path"])

    # 1. we first put the descriptive result (by using `describe()` function) of `refined_df` and possibly `renewed_df` into the `messages` list
    # 2. then, we instantiate a structured output model (GPT-4.1 is fine) and query it, based on the current messages, whether user's question can be solved - at this step, the messages should only contain an initial user prompt / query and the descriptive result. if the descriptive result is enough, we simply go to step 6.
    # 3. if the descriptive result is not enough, we use the `data_analysis_plan`, which is included in `abstract_plan_desc`, to work as the RAG query, and use the existed rag module (at `vector_search/dbmanager.py`) to retrieve the top 3 most related functions.
    # 4. execute the retrieved functions. they should follow a similar execution logic as in `data_engineering` node. that is, we need to curate some pydantic schema to describe the function parameters, and then use a structured output model to determine the parameters. notably, here the `df` could be either `refined_df` or `renewed_df`, and this has to be mentioned in the schema and prompt for the model to determine which one to use.
    # 5. after executing the retrieved functions, and enqueue their results into the `messages` list, we again query the structured output model whether the user's question can be solved. if yes, we return the result. if not, we go back to a fallback solution - we ask a code generation model to generate the code to meet the `data_analysis_plan` requirement.
    # 6. at this step, in either case (step 2 or 5), we have enough information to answer user's question. so we simply use the full `messages` to query a expert LLM model to generate the final answer. the prompt has to be carefully curated to correctly include the numeric details.

    return state


# --- Graph Definition ---
builder = StateGraph(AgentState)
builder.add_node("standby", standby)
builder.add_node("abstract_plan", abstract_plan)
builder.add_node("data_engineering", data_engineering)
builder.add_node("data_analysis", data_analysis)
builder.add_edge(START, "standby")
builder.add_conditional_edges("standby", standby_routing)
builder.add_edge("abstract_plan", "data_engineering")
builder.add_edge("data_engineering", "data_analysis")
builder.add_edge("data_analysis", END)

# --- Main Execution Block ---
if __name__ == "__main__":
    print(REGISTERED_TOOLS)
    print("--------------------------------")

    conn = sqlite3.connect("cache.db", check_same_thread=False)
    try:
        checkpointer = SqliteSaver(conn=conn)
        graph = builder.compile(
            checkpointer=checkpointer, interrupt_before=["data_analysis"]
        )

        # --- Configuration ---
        query = "How does the shift duration change over time?"
        file_path = "../../../test/shift.csv"
        input_string = f"{query}|{file_path}"
        thread_id = hashlib.sha256(input_string.encode()).hexdigest()
        config = {"configurable": {"thread_id": thread_id}}

        # --- Check for existing state to decide whether to start or resume ---
        existing_state = checkpointer.get(config)

        final_result = None
        if existing_state is None:
            # FIRST RUN: No state exists, so we start a new run with the initial input.
            print(
                f"--- No checkpoint found for thread '{thread_id}'. Starting new run. ---"
            )
            initial_input = {
                "current_user_prompt": query,
                "target_data_sources": [file_path],
            }
            # Use .stream() to see each step
            for chunk in graph.stream(initial_input, config=config):
                # A chunk is a dictionary with the node name as the key
                node_name = list(chunk.keys())[0]
                pprint({node_name: chunk[node_name]})
                final_result = chunk

        else:
            # SECOND RUN: State exists, so we resume from where we left off.
            print(
                f"--- Checkpoint found for thread '{thread_id}'. Resuming run. ---"
            )
            # Use .stream() with None to see each step during resumption
            for chunk in graph.stream(None, config=config):
                node_name = list(chunk.keys())[0]
                pprint({node_name: chunk[node_name]})
                final_result = chunk

        print("\n--- Execution Complete ---")
        if final_result:
            # The final result is the output of the last node that ran
            last_node = list(final_result.keys())[0]
            if last_node == "data_analysis":
                print("Successfully resumed and ran the 'data_analysis' node.")
            else:
                print(f"Execution stopped at node '{last_node}'.")

    finally:
        conn.close()
